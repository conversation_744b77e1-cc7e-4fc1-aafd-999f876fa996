"use client";

import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { BackButton } from "@/components/back-button";
import { BreadcrumbNav } from "@/components/breadcrumb-nav";

export function SubNavigation() {
  const pathname = usePathname();
  const [isHydrated, setIsHydrated] = useState(false);

  // Handle hydration
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Don't render anything until hydrated to prevent mismatch
  if (!isHydrated) {
    return null;
  }

  // Don't show navigation on home page
  if (pathname === "/") {
    return null;
  }

  return (
    <div className="px-4 py-3">
      <div className="flex items-center gap-4">
        <BackButton />
        <BreadcrumbNav />
      </div>
    </div>
  );
}
